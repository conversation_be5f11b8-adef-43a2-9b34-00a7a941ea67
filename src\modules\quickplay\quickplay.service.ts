// Common imports
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@common/helpers/cache.helper';
import { SocialCode, UserAccountType } from '@constants/user';
import { SocialInfoDto } from '@modules/auth/dto/social-info.dto.ts';
import { VerifyContactEvent } from '@modules/auth/events/auth.events.ts';
import { GameEntity } from '@modules/game/game.entity.ts';
import { TransactionHistoryDto } from '@modules/payment/dtos/transaction-history.dto';
import type { TransactionHistoryOptionsDto } from '@modules/payment/dtos/transaction-history-options.dto';
import { PaymentTransactionEntity } from '@modules/payment/payment-transaction.entity';
import { Injectable } from '@nestjs/common';
import { CommandBus, EventBus } from '@nestjs/cqrs';
import { InjectRepository } from '@nestjs/typeorm';
import { OtpService } from '@shared/services/otp.service.ts';
import { RedisService } from '@shared/services/redis.service';
import { RedisKeyManagerService } from '@shared/services/redis-key-manager.service';
import { plainToClass } from 'class-transformer';
import {
    AccountAlreadyLinkedException,
    ContactConflictException,
    ContactRequiredException,
    InvalidUserTypeException,
    NotFoundException,
    UserAlreadyExistsException,
    UserNotFoundException,
    UserNotLinkedException,
} from 'exceptions';
import { GeneratorProvider } from 'providers/generator.provider.ts';
import type { FindManyOptions, FindOptionsWhere } from 'typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';

import { ApiConfigService } from '../../shared/services/api-config.service.ts';
import { UserRegisterDto } from '../auth/dto/user-register.dto.ts';
import { UserRegisterSsoDto } from '../auth/dto/user-register-sso.dto.ts';
import { CreateUserProfileCommand } from './commands/create-user-profile.command.ts';
import { MutateUserProfileDto } from './dtos/mutate-user-profile.dto.ts';
import type { UserQuickplayLinkDto } from './dtos/user-quickplay-link.dto.ts';
import type { UserQuickplayLoginDto } from './dtos/user-quickplay-login.dto.ts';
import { UserQuickplayResponseDto } from './dtos/user-quickplay-response.dto.ts';
import { UserProfileUpdatedEvent } from './events/user.events.ts';
import { UserProfileNotFoundException } from './exceptions/user-profile-not-found.exception.ts';
import { UserAccountEntity } from './user-account.entity.ts';
import { UserGameMappingEntity } from './user-game-mapping.entity.ts';
import { UserProfileEntity } from './user-profile.entity.ts';
import type { QuickplayDto } from './dtos/quickplay.dto.ts';
import { QuickplayResponseDto } from './dtos/quickplay-response.dto.ts';

@Injectable()
export class QuickplayService {
	constructor(
		@InjectRepository(UserAccountEntity)
		private userRepository: Repository<UserAccountEntity>,
		@InjectRepository(UserProfileEntity)
		private userProfileRepository: Repository<UserProfileEntity>,
		@InjectRepository(PaymentTransactionEntity)
		private paymentTransactionRepository: Repository<PaymentTransactionEntity>,
		@InjectRepository(UserGameMappingEntity)
		private userGameMappingRepository: Repository<UserGameMappingEntity>,
		@InjectRepository(GameEntity)
		private gameRepository: Repository<GameEntity>,
		private commandBus: CommandBus,
		private eventBus: EventBus,
		private configService: ApiConfigService,
		private redisService: RedisService,
		private redisKeyManager: RedisKeyManagerService,
		private cacheHelper: CacheHelper,
		private otpService: OtpService,
	) {}

	async createUserQuickplay(
		userQuickplayDto: UserQuickplayLoginDto,
		ip: string,
	): Promise<UserQuickplayResponseDto> {
		const username = `${SocialCode.get(
			UserAccountType.QUICKPLAY,
		)}_${GeneratorProvider.uuidNoHyphens()}`;

		const user = this.userRepository.create({
			username,
			email: null,
			accountType: UserAccountType.QUICKPLAY,
			createdAtIp: ip,
		});

		const rawPassword = `${userQuickplayDto.platform}-${userQuickplayDto.uniqueId}-${userQuickplayDto.deviceId}`;
		user.passwordHash = rawPassword;

		const savedUser = await this.userRepository.save(user);

		return new QuickplayResponseDto({
			quickplayId: username,
		});
	}

	async loginQuickplay(
		quickplayDto: QuickplayDto,
		ip: string,
	): Promise<QuickplayResponseDto> {
		const quickplayUsername = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${quickplayDto.quickplayId}`;

		const user = await this.findOne({
			username: quickplayUsername,
			accountType: UserAccountType.QUICKPLAY,
		});

		if (!user) {
			throw new UserNotFoundException('Không tìm thấy người dùng Quickplay');
		}

		await this.userRepository.update(user.userId, {
			lastLoginAt: new Date(),
			lastLoginAtIp: ip,
		});

		return new UserQuickplayResponseDto({
			qpId: user.username!,
			qpToken: userQuickplayDto.uniqueId,
			createdAt: user.createdAt,
		});
	}

	async linkUserQuickplay(
		userQuickplayDto: UserQuickplayLinkDto,
		ip: string,
	): Promise<void> {
		const quickplayUsername = `${SocialCode.get(UserAccountType.QUICKPLAY)}_${userQuickplayDto.qpId}`;

		const user = await this.userRepository.findOneBy({
			username: quickplayUsername,
			accountType: UserAccountType.QUICKPLAY,
		});

		if (!user) {
			throw new UserNotFoundException('Không tìm thấy người dùng Quickplay');
		}

		if (user.accountType !== UserAccountType.QUICKPLAY) {
			throw new AccountAlreadyLinkedException('Tài khoản này đã được liên kết trước đó');
		}

		const existing = await this.userRepository.findOneBy({
			username: userQuickplayDto.username!,
		});

		if (existing) {
			throw new UserAlreadyExistsException('Username đã tồn tại');
		}

		user.lastLoginAt = new Date();
		user.lastLoginAtIp = ip;
		user.username = userQuickplayDto.username;
		user.passwordHash = userQuickplayDto.password;
		user.accountType = UserAccountType.LOCAL;

		await this.userRepository.save(user);

		if (!user.userProfile) {
			user.userProfile = await this.createUserProfile(
				user.userId,
				plainToClass(MutateUserProfileDto, {
					displayName: null,
					gender: null,
					avatarUrl: null,
					dob: null,
					address: null,
					identifierNumber: null,
				}),
			);
		}
	}
}