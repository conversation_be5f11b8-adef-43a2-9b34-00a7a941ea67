import { getIp } from '@common/utils';
import { ApiCommonResponse } from '@decorators/api-common-response.decorator';
import {
	Body,
	Controller,
	HttpCode,
	HttpStatus,
	Post,
	Req,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import type { Request } from 'express';

import { QuickplayDto } from './dtos/quickplay.dto';
import { QuickplayLinkDto } from './dtos/quickplay-link.dto';
import { QuickplayResponseDto } from './dtos/quickplay-response.dto';
import type { QuickplayService } from './quickplay.service';

@Controller('quickplay')
@ApiTags('Quickplay')
export class QuickplayController {
	constructor(private quickplayService: QuickplayService) { }

	@Post()
	@HttpCode(HttpStatus.OK)
	@ApiCommonResponse({
		type: QuickplayResponseDto,
		description: 'create quickplay user',
	})
	async quickplay(
		@Body() quickplayDto: QuickplayDto,
		@Req() request: Request,
	): Promise<QuickplayResponseDto> {
		if (quickplayDto.quickplayId) {
			return this.quickplayService.loginQuickplay(
				quickplayDto,
				getIp(request),
			);
		}
		return this.quickplayService.createQuickplay(
			quickplayDto,
			getIp(request),
		);
	}

	@Post('link')
	@HttpCode(HttpStatus.OK)
	@ApiCommonResponse({
		description: 'link quickplay user',
	})
	async quickplayLink(
		@Body() quickplayLinkDto: QuickplayLinkDto,
		@Req() request: Request,
	): Promise<void> {
		return this.userService.linkUserQuickplay(userQuickplayDto, getIp(request));
	}
}